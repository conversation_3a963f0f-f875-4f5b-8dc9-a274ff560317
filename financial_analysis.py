import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
import warnings
warnings.filterwarnings('ignore')

# Mac系统中文字体配置 - 基于fix_chinese_font.py的成功配置
import matplotlib.font_manager as fm
import platform
import os

def find_chinese_fonts():
    """查找系统中可用的中文字体（完全移植自fix_chinese_font.py）"""
    print("正在扫描系统字体...")
    
    # 获取所有字体
    font_list = fm.findSystemFonts()
    font_names = []
    
    for font_path in font_list:
        try:
            font_prop = fm.FontProperties(fname=font_path)
            font_name = font_prop.get_name()
            font_names.append((font_name, font_path))
        except:
            continue
    
    # Mac系统常见中文字体
    chinese_fonts = [
        'PingFang SC',
        'PingFang TC', 
        'Hiragino Sans GB',
        'Hiragino Sans',
        'STHeiti',
        'STSong',
        'STKaiti',
        'STFangsong',
        'Heiti SC',
        'Heiti TC',
        'Songti SC',
        'Songti TC',
        'Kai<PERSON> SC',
        'Kaiti TC',
        'Arial Unicode MS',
        'Apple LiGothic',
        'Apple LiSung',
        'SimHei',
        'SimSun',
        'Microsoft YaHei'
    ]
    
    # 查找可用的中文字体
    available_chinese_fonts = []
    for font_name, font_path in font_names:
        for chinese_font in chinese_fonts:
            if chinese_font.lower() in font_name.lower():
                available_chinese_fonts.append((font_name, font_path))
                break
    
    # 去重
    unique_fonts = {}
    for font_name, font_path in available_chinese_fonts:
        if font_name not in unique_fonts:
            unique_fonts[font_name] = font_path
    
    return unique_fonts

def test_font(font_name):
    """测试指定字体的中文显示效果"""
    try:
        plt.rcParams['font.sans-serif'] = [font_name]
        plt.rcParams['axes.unicode_minus'] = False
        print(f"✓ 字体 '{font_name}' 配置成功")
        return True
    except Exception as e:
        print(f"✗ 字体 '{font_name}' 配置失败: {e}")
        return False

def find_best_chinese_font():
    """配置最佳的中文字体（移植自fix_chinese_font.py）"""
    print("=== 查找最佳中文字体 ===")
    
    # 检测系统
    system = platform.system()
    print(f"系统: {system}")
    
    # 查找中文字体
    chinese_fonts = find_chinese_fonts()
    
    if not chinese_fonts:
        print("❌ 未找到中文字体！")
        return 'PingFang SC'  # 默认字体
    
    print(f"\n找到 {len(chinese_fonts)} 个中文字体:")
    for i, font_name in enumerate(chinese_fonts.keys(), 1):
        print(f"{i}. {font_name}")
    
    # 推荐字体优先级（与fix_chinese_font.py一致）
    recommended_fonts = [
        'PingFang SC',
        'Hiragino Sans GB', 
        'STHeiti',
        'Arial Unicode MS',
        'Heiti SC'
    ]
    
    # 找到最佳字体
    best_font = None
    for font in recommended_fonts:
        if font in chinese_fonts:
            best_font = font
            break
    
    if not best_font:
        best_font = list(chinese_fonts.keys())[0]
    
    print(f"\n推荐使用字体: {best_font}")
    
    # 测试最佳字体
    print(f"\n正在测试字体 '{best_font}'...")
    if test_font(best_font):
        print(f"✅ 字体 '{best_font}' 测试成功！")
        return best_font
    else:
        print(f"❌ 字体 '{best_font}' 测试失败")
        return 'PingFang SC'

def configure_chinese_font():
    """配置中文字体显示（完全移植自fix_chinese_font.py的成功配置）"""
    print("=== 配置中文字体显示 ===")
    
    # 清除matplotlib字体缓存（兼容不同版本）
    try:
        # 尝试新版本的方法
        if hasattr(fm, 'get_cachedir'):
            cache_dir = fm.get_cachedir()
        else:
            # 兼容旧版本
            cache_dir = fm.fontManager.cachedir

        if cache_dir and os.path.exists(cache_dir):
            import shutil
            shutil.rmtree(cache_dir)
            print("已清除matplotlib字体缓存")
            # 重建字体缓存
            if hasattr(fm.fontManager, '__init__'):
                fm.fontManager.__init__()
            else:
                # 重新加载字体管理器
                fm._rebuild()
    except Exception as e:
        print(f"清除字体缓存时出现问题: {e}")

    # 查找最佳字体
    best_font = find_best_chinese_font()

    # 配置字体（与fix_chinese_font.py完全一致的配置）
    plt.rcParams['font.sans-serif'] = [best_font, 'DejaVu Sans', 'Helvetica', 'Arial']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.size'] = 10

    # 设置图形样式
    plt.style.use('default')

    print(f"字体配置完成: {best_font}")
    
    # 创建配置文件（与fix_chinese_font.py一致）
    config_code = f'''
# Mac系统matplotlib中文字体配置
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['{best_font}', 'DejaVu Sans', 'Helvetica', 'Arial']
plt.rcParams['axes.unicode_minus'] = False

print("中文字体配置完成: {best_font}")
'''
    
    with open('matplotlib_chinese_config.py', 'w', encoding='utf-8') as f:
        f.write(config_code)
    
    print(f"配置代码已保存到: matplotlib_chinese_config.py")
    
    return best_font

# 导入已配置好的字体设置
try:
    from matplotlib_chinese_config import *
    print("已导入字体配置")
except ImportError:
    print("字体配置文件不存在，使用默认配置")
    # 执行字体配置
    current_font = configure_chinese_font()

def ensure_chinese_font():
    """确保中文字体在绘图前正确配置"""
    # 使用已测试成功的字体配置
    plt.rcParams['font.sans-serif'] = ['Hiragino Sans GB', 'DejaVu Sans', 'Helvetica', 'Arial']
    plt.rcParams['axes.unicode_minus'] = False
    print(f"已重新配置字体: Hiragino Sans GB")

# 验证字体设置
def verify_font():
    """验证字体设置是否正确"""
    current_font = plt.rcParams['font.sans-serif'][0]
    print(f"当前主字体: {current_font}")
    return True

verify_font()

class FinancialAnalyzer:
    def __init__(self, data_folder):
        self.data_folder = data_folder
        self.companies = ['上海环境公司', '远达环保', '首创环保', '龙净环保']
        self.financial_data = {}
        
    def load_data(self):
        """加载所有公司的财务数据"""
        print("正在加载财务数据...")
        
        for company in self.companies:
            self.financial_data[company] = {}
            
            # 加载利润表
            try:
                profit_file = f"{self.data_folder}/{company}--利润表.xlsx"
                if company == '首创环保':
                    profit_file = f"{self.data_folder}/{company}利润表.xlsx"
                elif company == '龙净环保':
                    profit_file = f"{self.data_folder}/{company}--利润表.xlsx"
                    
                self.financial_data[company]['利润表'] = pd.read_excel(profit_file)
                print(f"已加载 {company} 利润表")
            except Exception as e:
                print(f"加载 {company} 利润表失败: {e}")
                
            # 加载资产负债表
            try:
                balance_file = f"{self.data_folder}/{company}--资产负债表.xlsx"
                if company == '首创环保':
                    balance_file = f"{self.data_folder}/{company}资产负债表.xlsx"
                elif company == '龙净环保':
                    balance_file = f"{self.data_folder}/{company}-资产负债表.xlsx"
                    
                self.financial_data[company]['资产负债表'] = pd.read_excel(balance_file)
                print(f"已加载 {company} 资产负债表")
            except Exception as e:
                print(f"加载 {company} 资产负债表失败: {e}")
                
            # 加载现金流量表
            try:
                cashflow_file = f"{self.data_folder}/{company}--现金流量表.xlsx"
                if company == '首创环保':
                    cashflow_file = f"{self.data_folder}/{company}现金流量表.xlsx"
                elif company == '龙净环保':
                    cashflow_file = f"{self.data_folder}/{company}-现金流量表.xlsx"
                    
                self.financial_data[company]['现金流量表'] = pd.read_excel(cashflow_file)
                print(f"已加载 {company} 现金流量表")
            except Exception as e:
                print(f"加载 {company} 现金流量表失败: {e}")
                
        print("数据加载完成！\n")
        
    def explore_data_structure(self):
        """探索数据结构"""
        print("=== 数据结构探索 ===")
        for company in self.companies:
            print(f"\n{company}:")
            for statement_type in ['利润表', '资产负债表', '现金流量表']:
                if statement_type in self.financial_data[company]:
                    df = self.financial_data[company][statement_type]
                    print(f"  {statement_type}: {df.shape}")
                    print(f"    列名: {list(df.columns)[:5]}...")  # 显示前5列
                    
    def clean_and_prepare_data(self):
        """数据清洗和准备"""
        print("=== 开始数据清洗 ===")
        
        for company in self.companies:
            print(f"\n处理 {company} 的数据...")
            
            for statement_type in ['利润表', '资产负债表', '现金流量表']:
                if statement_type in self.financial_data[company]:
                    df = self.financial_data[company][statement_type]
                    
                    # 检查空值
                    null_count = df.isnull().sum().sum()
                    print(f"  {statement_type} 空值数量: {null_count}")
                    
                    # 填充空值
                    if null_count > 0:
                        # 对数值列使用0填充，对文本列使用'未知'填充
                        numeric_cols = df.select_dtypes(include=[np.number]).columns
                        text_cols = df.select_dtypes(include=['object']).columns
                        
                        df[numeric_cols] = df[numeric_cols].fillna(0)
                        df[text_cols] = df[text_cols].fillna('未知')
                        
                        print(f"    已填充空值")
                    
                    # 更新数据
                    self.financial_data[company][statement_type] = df
                    
        print("数据清洗完成！\n")

    def examine_sample_data(self):
        """查看样本数据"""
        print("=== 样本数据查看 ===")

        # 查看第一家公司的数据结构
        company = self.companies[0]
        print(f"\n{company} 利润表前几行:")
        if '利润表' in self.financial_data[company]:
            df = self.financial_data[company]['利润表']
            print(df.head())
            print(f"\n列名: {list(df.columns)}")

        print(f"\n{company} 资产负债表前几行:")
        if '资产负债表' in self.financial_data[company]:
            df = self.financial_data[company]['资产负债表']
            print(df.head())

    def extract_financial_indicators(self):
        """提取财务指标"""
        print("=== 提取财务指标 ===")

        self.indicators = {}

        for company in self.companies:
            print(f"\n处理 {company}...")
            self.indicators[company] = {}

            # 获取各报表数据
            profit_df = self.financial_data[company].get('利润表')
            balance_df = self.financial_data[company].get('资产负债表')
            cashflow_df = self.financial_data[company].get('现金流量表')

            if profit_df is not None and balance_df is not None:
                # 获取年份列（排除第一列项目名称）
                years = [col for col in profit_df.columns if col != profit_df.columns[0]]

                for year in years:
                    year_str = str(year)[:4] if hasattr(year, 'year') else str(year)

                    if year_str not in self.indicators[company]:
                        self.indicators[company][year_str] = {}

                    # 从利润表提取数据
                    营业收入 = self.get_value_by_keyword(profit_df, ['营业收入', '主营业务收入'], year)
                    净利润 = self.get_value_by_keyword(profit_df, ['净利润', '归属于母公司所有者的净利润'], year)
                    营业成本 = self.get_value_by_keyword(profit_df, ['营业成本', '主营业务成本'], year)

                    # 从资产负债表提取数据
                    总资产 = self.get_value_by_keyword(balance_df, ['资产总计', '总资产'], year)
                    总负债 = self.get_value_by_keyword(balance_df, ['负债合计', '总负债'], year)
                    股东权益 = self.get_value_by_keyword(balance_df, ['所有者权益合计', '股东权益合计', '归属于母公司所有者权益合计'], year)
                    流动资产 = self.get_value_by_keyword(balance_df, ['流动资产合计'], year)
                    流动负债 = self.get_value_by_keyword(balance_df, ['流动负债合计'], year)
                    应收账款 = self.get_value_by_keyword(balance_df, ['应收账款', '应收票据及应收账款'], year)
                    存货 = self.get_value_by_keyword(balance_df, ['存货'], year)

                    # 计算财务指标
                    indicators = self.indicators[company][year_str]

                    # 盈利能力指标
                    indicators['营业收入'] = 营业收入
                    indicators['净利润'] = 净利润
                    indicators['净利润率'] = 净利润 / 营业收入 if 营业收入 != 0 else 0
                    indicators['ROA'] = 净利润 / 总资产 if 总资产 != 0 else 0
                    indicators['ROE'] = 净利润 / 股东权益 if 股东权益 != 0 else 0

                    # 偿债能力指标
                    indicators['总资产'] = 总资产
                    indicators['总负债'] = 总负债
                    indicators['股东权益'] = 股东权益
                    indicators['流动比率'] = 流动资产 / 流动负债 if 流动负债 != 0 else 0
                    速动资产 = 流动资产 - 存货 - 应收账款
                    indicators['速动比率'] = 速动资产 / 流动负债 if 流动负债 != 0 else 0
                    indicators['资产负债率'] = 总负债 / 总资产 if 总资产 != 0 else 0

                    # 营运能力指标（简化计算）
                    indicators['应收账款周转天数'] = 63  # 使用题目给定的行业平均值
                    indicators['应付账款周转天数'] = 90
                    indicators['存货周转天数'] = 40
                    indicators['营运周期'] = 63 + 40 - 90  # 13天

        print("财务指标提取完成！")

    def get_value_by_keyword(self, df, keywords, year_col):
        """根据关键词从数据框中获取值"""
        try:
            for keyword in keywords:
                # 在第一列中查找包含关键词的行
                mask = df.iloc[:, 0].astype(str).str.contains(keyword, na=False)
                if mask.any():
                    row_idx = mask.idxmax()
                    value = df.loc[row_idx, year_col]
                    # 转换为数值
                    if pd.isna(value) or value == 0 or value == '未知' or value == '--':
                        return 0
                    return self.convert_to_number(value)
            return 0
        except:
            return 0

    def convert_to_number(self, value):
        """将中文数值格式转换为数字（万元为单位）"""
        try:
            if isinstance(value, (int, float)):
                return float(value)

            value_str = str(value).strip()
            if value_str in ['--', '未知', '', 'nan']:
                return 0

            # 处理亿、万等单位
            if '亿' in value_str:
                num = float(value_str.replace('亿', ''))
                return num * 10000  # 转换为万元
            elif '万' in value_str:
                num = float(value_str.replace('万', ''))
                return num
            else:
                return float(value_str)
        except:
            return 0

    def calculate_growth_rates(self):
        """计算发展能力指标（增长率）"""
        print("=== 计算发展能力指标 ===")

        for company in self.companies:
            print(f"处理 {company}...")
            years = sorted(self.indicators[company].keys())

            for i in range(1, len(years)):
                current_year = years[i]
                previous_year = years[i-1]

                # 计算资产增长率
                current_assets = self.indicators[company][current_year]['总资产']
                previous_assets = self.indicators[company][previous_year]['总资产']

                if previous_assets != 0:
                    asset_growth = (current_assets - previous_assets) / previous_assets
                    self.indicators[company][current_year]['资产增长率'] = asset_growth
                else:
                    self.indicators[company][current_year]['资产增长率'] = 0

                # 计算营业收入增长率
                current_revenue = self.indicators[company][current_year]['营业收入']
                previous_revenue = self.indicators[company][previous_year]['营业收入']

                if previous_revenue != 0:
                    revenue_growth = (current_revenue - previous_revenue) / previous_revenue
                    self.indicators[company][current_year]['营业收入增长率'] = revenue_growth
                else:
                    self.indicators[company][current_year]['营业收入增长率'] = 0

                # 计算净利润增长率
                current_profit = self.indicators[company][current_year]['净利润']
                previous_profit = self.indicators[company][previous_year]['净利润']

                if previous_profit != 0:
                    profit_growth = (current_profit - previous_profit) / previous_profit
                    self.indicators[company][current_year]['净利润增长率'] = profit_growth
                else:
                    self.indicators[company][current_year]['净利润增长率'] = 0

        print("发展能力指标计算完成！")

    def create_summary_dataframe(self):
        """创建汇总数据框"""
        print("=== 创建汇总数据 ===")

        # 创建各项指标的汇总表
        summary_data = []

        for company in self.companies:
            for year, indicators in self.indicators[company].items():
                row = {
                    '公司': company,
                    '年份': year,
                    **indicators
                }
                summary_data.append(row)

        self.summary_df = pd.DataFrame(summary_data)

        # 保存到Excel
        self.summary_df.to_excel('财务指标汇总表.xlsx', index=False)
        print("汇总数据已保存到 财务指标汇总表.xlsx")

        return self.summary_df

    def create_pivot_tables(self):
        """创建数据透视表分析"""
        print("=== 创建数据透视表分析 ===")

        if not hasattr(self, 'summary_df'):
            self.create_summary_dataframe()

        # 创建Excel文件，包含多个工作表
        with pd.ExcelWriter('财务分析数据透视表.xlsx', engine='openpyxl') as writer:

            # 1. 原始数据表
            self.summary_df.to_excel(writer, sheet_name='原始数据', index=False)

            # 2. 盈利能力透视表
            profitability_pivot = pd.pivot_table(
                self.summary_df,
                values=['净利润率', 'ROA', 'ROE'],
                index='公司',
                columns='年份',
                aggfunc='mean'
            )
            profitability_pivot.to_excel(writer, sheet_name='盈利能力透视表')

            # 3. 偿债能力透视表
            solvency_pivot = pd.pivot_table(
                self.summary_df,
                values=['流动比率', '速动比率', '资产负债率'],
                index='公司',
                columns='年份',
                aggfunc='mean'
            )
            solvency_pivot.to_excel(writer, sheet_name='偿债能力透视表')

            # 4. 发展能力透视表
            if '资产增长率' in self.summary_df.columns:
                growth_pivot = pd.pivot_table(
                    self.summary_df,
                    values=['资产增长率', '营业收入增长率', '净利润增长率'],
                    index='公司',
                    columns='年份',
                    aggfunc='mean'
                )
                growth_pivot.to_excel(writer, sheet_name='发展能力透视表')

            # 5. 规模对比透视表
            scale_pivot = pd.pivot_table(
                self.summary_df,
                values=['营业收入', '净利润', '总资产'],
                index='公司',
                columns='年份',
                aggfunc='mean'
            )
            scale_pivot.to_excel(writer, sheet_name='规模对比透视表')

            # 6. 综合排名表
            latest_data = self.summary_df.groupby('公司').last()

            # 计算各项能力得分
            ranking_data = pd.DataFrame(index=latest_data.index)
            ranking_data['盈利能力得分'] = (latest_data['ROE'] * 100 / 15 * 25).clip(0, 25)
            ranking_data['偿债能力得分'] = ((2 - latest_data['资产负债率']) * 25).clip(0, 25)
            ranking_data['营运能力得分'] = 25  # 相同
            ranking_data['发展能力得分'] = 25 if '资产增长率' in latest_data.columns else 20
            ranking_data['综合得分'] = ranking_data.sum(axis=1)
            ranking_data = ranking_data.sort_values('综合得分', ascending=False)

            ranking_data.to_excel(writer, sheet_name='综合排名表')

        print("数据透视表已保存到 财务分析数据透视表.xlsx")

    def create_four_abilities_pivot_tables(self):
        """创建四个能力的专门数据透视表"""
        print("=== 创建四个能力专门数据透视表 ===")

        if not hasattr(self, 'summary_df'):
            self.create_summary_dataframe()

        # 创建四个能力的专门Excel文件
        with pd.ExcelWriter('四个能力数据透视表.xlsx', engine='openpyxl') as writer:

            # 1. 盈利能力数据透视表
            print("创建盈利能力数据透视表...")
            profitability_data = self.summary_df[['公司', '年份', '营业收入', '净利润', '净利润率', 'ROA', 'ROE']].copy()
            profitability_data['净利润率(%)'] = profitability_data['净利润率'] * 100
            profitability_data['ROA(%)'] = profitability_data['ROA'] * 100
            profitability_data['ROE(%)'] = profitability_data['ROE'] * 100

            # 盈利能力汇总表
            profitability_summary = profitability_data.groupby('公司').agg({
                '营业收入': ['mean', 'std', 'min', 'max'],
                '净利润': ['mean', 'std', 'min', 'max'],
                '净利润率(%)': ['mean', 'std', 'min', 'max'],
                'ROA(%)': ['mean', 'std', 'min', 'max'],
                'ROE(%)': ['mean', 'std', 'min', 'max']
            }).round(2)

            profitability_data.to_excel(writer, sheet_name='盈利能力原始数据', index=False)
            profitability_summary.to_excel(writer, sheet_name='盈利能力统计汇总')

            # 2. 偿债能力数据透视表
            print("创建偿债能力数据透视表...")
            solvency_data = self.summary_df[['公司', '年份', '总资产', '总负债', '股东权益', '流动比率', '速动比率', '资产负债率']].copy()
            solvency_data['资产负债率(%)'] = solvency_data['资产负债率'] * 100

            # 偿债能力汇总表
            solvency_summary = solvency_data.groupby('公司').agg({
                '总资产': ['mean', 'std', 'min', 'max'],
                '总负债': ['mean', 'std', 'min', 'max'],
                '股东权益': ['mean', 'std', 'min', 'max'],
                '流动比率': ['mean', 'std', 'min', 'max'],
                '速动比率': ['mean', 'std', 'min', 'max'],
                '资产负债率(%)': ['mean', 'std', 'min', 'max']
            }).round(2)

            solvency_data.to_excel(writer, sheet_name='偿债能力原始数据', index=False)
            solvency_summary.to_excel(writer, sheet_name='偿债能力统计汇总')

            # 3. 营运能力数据透视表
            print("创建营运能力数据透视表...")
            operational_data = pd.DataFrame({
                '公司': self.companies,
                '应收账款周转天数': [63] * len(self.companies),
                '存货周转天数': [40] * len(self.companies),
                '应付账款周转天数': [90] * len(self.companies),
                '营运周期': [13] * len(self.companies),
                '评价': ['资金周转效率高'] * len(self.companies)
            })

            operational_data.to_excel(writer, sheet_name='营运能力数据', index=False)

            # 4. 发展能力数据透视表
            print("创建发展能力数据透视表...")
            if '资产增长率' in self.summary_df.columns:
                growth_data = self.summary_df[['公司', '年份', '资产增长率', '营业收入增长率', '净利润增长率']].copy()
                growth_data = growth_data.dropna()

                if not growth_data.empty:
                    growth_data['资产增长率(%)'] = growth_data['资产增长率'] * 100
                    growth_data['营业收入增长率(%)'] = growth_data['营业收入增长率'] * 100
                    growth_data['净利润增长率(%)'] = growth_data['净利润增长率'] * 100

                    # 发展能力汇总表
                    growth_summary = growth_data.groupby('公司').agg({
                        '资产增长率(%)': ['mean', 'std', 'min', 'max'],
                        '营业收入增长率(%)': ['mean', 'std', 'min', 'max'],
                        '净利润增长率(%)': ['mean', 'std', 'min', 'max']
                    }).round(2)

                    growth_data.to_excel(writer, sheet_name='发展能力原始数据', index=False)
                    growth_summary.to_excel(writer, sheet_name='发展能力统计汇总')

            # 5. 四个能力综合评分表
            print("创建综合评分表...")
            latest_data = self.summary_df.groupby('公司').last()

            comprehensive_score = pd.DataFrame(index=latest_data.index)
            comprehensive_score['盈利能力得分'] = (latest_data['ROE'] * 100 / 15 * 25).clip(0, 25).round(1)
            comprehensive_score['偿债能力得分'] = ((2 - latest_data['资产负债率']) * 25).clip(0, 25).round(1)
            comprehensive_score['营运能力得分'] = 25.0  # 相同标准

            if '资产增长率' in latest_data.columns:
                comprehensive_score['发展能力得分'] = ((latest_data['资产增长率'] + 0.1) * 25 / 0.2).clip(0, 25).round(1)
            else:
                comprehensive_score['发展能力得分'] = 20.0

            comprehensive_score['综合得分'] = comprehensive_score.sum(axis=1).round(1)
            comprehensive_score['排名'] = comprehensive_score['综合得分'].rank(ascending=False, method='min').astype(int)

            # 添加评级
            def get_rating(score):
                if score >= 90:
                    return 'A+'
                elif score >= 80:
                    return 'A'
                elif score >= 70:
                    return 'B+'
                elif score >= 60:
                    return 'B'
                else:
                    return 'C'

            comprehensive_score['评级'] = comprehensive_score['综合得分'].apply(get_rating)
            comprehensive_score = comprehensive_score.sort_values('综合得分', ascending=False)

            comprehensive_score.to_excel(writer, sheet_name='四个能力综合评分')

        print("四个能力数据透视表已保存到 四个能力数据透视表.xlsx")

    def create_visualizations(self):
        """创建可视化图表"""
        print("=== 创建可视化图表 ===")

        if not hasattr(self, 'summary_df'):
            self.create_summary_dataframe()

        # 设置图形参数
        plt.rcParams['figure.figsize'] = (12, 8)

        # 1. 盈利能力分析
        self.plot_profitability_analysis()

        # 2. 偿债能力分析
        self.plot_solvency_analysis()

        # 3. 营运能力分析
        self.plot_operational_analysis()

        # 4. 发展能力分析
        self.plot_growth_analysis()

        # 5. 综合对比分析
        self.plot_comprehensive_comparison()

        print("所有图表已生成完成！")



    def plot_profitability_analysis(self):
        """盈利能力分析图表"""
        # 确保字体配置正确
        ensure_chinese_font()
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('四家公司盈利能力分析', fontsize=16, fontweight='bold')

        # 净利润率趋势图
        ax1 = axes[0, 0]
        for company in self.companies:
            company_data = self.summary_df[self.summary_df['公司'] == company]
            if not company_data.empty:
                ax1.plot(company_data['年份'], company_data['净利润率'] * 100,
                        marker='o', linewidth=2, label=company)
        ax1.set_title('净利润率趋势(%)')
        ax1.set_xlabel('年份')
        ax1.set_ylabel('净利润率(%)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # ROA趋势图
        ax2 = axes[0, 1]
        for company in self.companies:
            company_data = self.summary_df[self.summary_df['公司'] == company]
            if not company_data.empty:
                ax2.plot(company_data['年份'], company_data['ROA'] * 100,
                        marker='s', linewidth=2, label=company)
        ax2.set_title('ROA趋势(%)')
        ax2.set_xlabel('年份')
        ax2.set_ylabel('ROA(%)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # ROE趋势图
        ax3 = axes[1, 0]
        for company in self.companies:
            company_data = self.summary_df[self.summary_df['公司'] == company]
            if not company_data.empty:
                ax3.plot(company_data['年份'], company_data['ROE'] * 100,
                        marker='^', linewidth=2, label=company)
        ax3.set_title('ROE趋势(%)')
        ax3.set_xlabel('年份')
        ax3.set_ylabel('ROE(%)')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 最新年份盈利能力对比柱状图
        ax4 = axes[1, 1]
        latest_data = self.summary_df.groupby('公司').last()
        companies = latest_data.index
        roe_values = latest_data['ROE'] * 100

        bars = ax4.bar(companies, roe_values, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])
        ax4.set_title('最新年份ROE对比(%)')
        ax4.set_ylabel('ROE(%)')
        ax4.tick_params(axis='x', rotation=45)

        # 在柱状图上添加数值标签
        for bar, value in zip(bars, roe_values):
            ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                    f'{value:.2f}%', ha='center', va='bottom')

        plt.tight_layout()
        plt.savefig('盈利能力分析.png', dpi=300, bbox_inches='tight')
        plt.show()

    def plot_solvency_analysis(self):
        """偿债能力分析图表"""
        # 确保字体配置正确
        ensure_chinese_font()
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('四家公司偿债能力分析', fontsize=16, fontweight='bold')

        # 流动比率趋势图
        ax1 = axes[0, 0]
        for company in self.companies:
            company_data = self.summary_df[self.summary_df['公司'] == company]
            if not company_data.empty:
                ax1.plot(company_data['年份'], company_data['流动比率'],
                        marker='o', linewidth=2, label=company)
        ax1.axhline(y=1.0, color='red', linestyle='--', alpha=0.7, label='安全线(1.0)')
        ax1.set_title('流动比率趋势')
        ax1.set_xlabel('年份')
        ax1.set_ylabel('流动比率')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 速动比率趋势图
        ax2 = axes[0, 1]
        for company in self.companies:
            company_data = self.summary_df[self.summary_df['公司'] == company]
            if not company_data.empty:
                ax2.plot(company_data['年份'], company_data['速动比率'],
                        marker='s', linewidth=2, label=company)
        ax2.axhline(y=1.0, color='red', linestyle='--', alpha=0.7, label='安全线(1.0)')
        ax2.set_title('速动比率趋势')
        ax2.set_xlabel('年份')
        ax2.set_ylabel('速动比率')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 资产负债率趋势图
        ax3 = axes[1, 0]
        for company in self.companies:
            company_data = self.summary_df[self.summary_df['公司'] == company]
            if not company_data.empty:
                ax3.plot(company_data['年份'], company_data['资产负债率'] * 100,
                        marker='^', linewidth=2, label=company)
        ax3.axhline(y=60, color='red', linestyle='--', alpha=0.7, label='警戒线(60%)')
        ax3.set_title('资产负债率趋势(%)')
        ax3.set_xlabel('年份')
        ax3.set_ylabel('资产负债率(%)')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 最新年份偿债能力对比饼图
        ax4 = axes[1, 1]
        latest_data = self.summary_df.groupby('公司').last()
        debt_ratios = latest_data['资产负债率'] * 100

        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
        wedges, texts, autotexts = ax4.pie(debt_ratios, labels=debt_ratios.index,
                                          autopct='%1.1f%%', colors=colors, startangle=90)
        ax4.set_title('最新年份资产负债率分布')

        plt.tight_layout()
        plt.savefig('偿债能力分析.png', dpi=300, bbox_inches='tight')
        plt.show()

    def plot_operational_analysis(self):
        """营运能力分析图表"""
        # 确保字体配置正确
        ensure_chinese_font()
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('四家公司营运能力分析', fontsize=16, fontweight='bold')

        # 营运周期对比柱状图
        ax1 = axes[0, 0]
        latest_data = self.summary_df.groupby('公司').last()
        companies = latest_data.index
        cycle_days = [13] * len(companies)  # 使用题目给定的营运周期

        bars = ax1.bar(companies, cycle_days, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])
        ax1.set_title('营运周期对比(天)')
        ax1.set_ylabel('天数')
        ax1.tick_params(axis='x', rotation=45)

        for bar, value in zip(bars, cycle_days):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                    f'{value}天', ha='center', va='bottom')

        # 应收账款周转天数
        ax2 = axes[0, 1]
        receivable_days = [63] * len(companies)
        bars2 = ax2.bar(companies, receivable_days, color='lightblue')
        ax2.set_title('应收账款周转天数')
        ax2.set_ylabel('天数')
        ax2.tick_params(axis='x', rotation=45)

        for bar, value in zip(bars2, receivable_days):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                    f'{value}天', ha='center', va='bottom')

        # 存货周转天数
        ax3 = axes[1, 0]
        inventory_days = [40] * len(companies)
        bars3 = ax3.bar(companies, inventory_days, color='lightgreen')
        ax3.set_title('存货周转天数')
        ax3.set_ylabel('天数')
        ax3.tick_params(axis='x', rotation=45)

        for bar, value in zip(bars3, inventory_days):
            ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                    f'{value}天', ha='center', va='bottom')

        # 应付账款周转天数
        ax4 = axes[1, 1]
        payable_days = [90] * len(companies)
        bars4 = ax4.bar(companies, payable_days, color='lightcoral')
        ax4.set_title('应付账款周转天数')
        ax4.set_ylabel('天数')
        ax4.tick_params(axis='x', rotation=45)

        for bar, value in zip(bars4, payable_days):
            ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 2,
                    f'{value}天', ha='center', va='bottom')

        plt.tight_layout()
        plt.savefig('营运能力分析.png', dpi=300, bbox_inches='tight')
        plt.show()

    def plot_growth_analysis(self):
        """发展能力分析图表"""
        # 确保字体配置正确
        ensure_chinese_font()
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('四家公司发展能力分析', fontsize=16, fontweight='bold')

        # 资产增长率趋势图
        ax1 = axes[0, 0]
        for company in self.companies:
            company_data = self.summary_df[self.summary_df['公司'] == company]
            if not company_data.empty and '资产增长率' in company_data.columns:
                valid_data = company_data.dropna(subset=['资产增长率'])
                if not valid_data.empty:
                    ax1.plot(valid_data['年份'], valid_data['资产增长率'] * 100,
                            marker='o', linewidth=2, label=company)
        ax1.set_title('资产增长率趋势(%)')
        ax1.set_xlabel('年份')
        ax1.set_ylabel('资产增长率(%)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.axhline(y=0, color='red', linestyle='--', alpha=0.5)

        # 营业收入增长率趋势图
        ax2 = axes[0, 1]
        for company in self.companies:
            company_data = self.summary_df[self.summary_df['公司'] == company]
            if not company_data.empty and '营业收入增长率' in company_data.columns:
                valid_data = company_data.dropna(subset=['营业收入增长率'])
                if not valid_data.empty:
                    ax2.plot(valid_data['年份'], valid_data['营业收入增长率'] * 100,
                            marker='s', linewidth=2, label=company)
        ax2.set_title('营业收入增长率趋势(%)')
        ax2.set_xlabel('年份')
        ax2.set_ylabel('营业收入增长率(%)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.axhline(y=0, color='red', linestyle='--', alpha=0.5)

        # 净利润增长率趋势图
        ax3 = axes[1, 0]
        for company in self.companies:
            company_data = self.summary_df[self.summary_df['公司'] == company]
            if not company_data.empty and '净利润增长率' in company_data.columns:
                valid_data = company_data.dropna(subset=['净利润增长率'])
                if not valid_data.empty:
                    ax3.plot(valid_data['年份'], valid_data['净利润增长率'] * 100,
                            marker='^', linewidth=2, label=company)
        ax3.set_title('净利润增长率趋势(%)')
        ax3.set_xlabel('年份')
        ax3.set_ylabel('净利润增长率(%)')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        ax3.axhline(y=0, color='red', linestyle='--', alpha=0.5)

        # 最新年份增长率对比
        ax4 = axes[1, 1]
        latest_growth = self.summary_df.groupby('公司').last()
        if '资产增长率' in latest_growth.columns:
            growth_rates = latest_growth['资产增长率'] * 100
            colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
            bars = ax4.bar(growth_rates.index, growth_rates, color=colors)
            ax4.set_title('最新年份资产增长率对比(%)')
            ax4.set_ylabel('资产增长率(%)')
            ax4.tick_params(axis='x', rotation=45)
            ax4.axhline(y=0, color='red', linestyle='--', alpha=0.5)

            for bar, value in zip(bars, growth_rates):
                ax4.text(bar.get_x() + bar.get_width()/2,
                        bar.get_height() + (0.5 if value >= 0 else -1),
                        f'{value:.1f}%', ha='center',
                        va='bottom' if value >= 0 else 'top')

        plt.tight_layout()
        plt.savefig('发展能力分析.png', dpi=300, bbox_inches='tight')
        plt.show()

    def plot_comprehensive_comparison(self):
        """综合对比分析图表"""
        # 确保字体配置正确
        ensure_chinese_font()
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('四家公司综合财务能力对比分析', fontsize=16, fontweight='bold')

        latest_data = self.summary_df.groupby('公司').last()
        companies = latest_data.index

        # 四维雷达图 - 综合能力对比
        ax1 = axes[0, 0]
        categories = ['盈利能力', '偿债能力', '营运能力', '发展能力']

        # 标准化各项指标到0-100分
        profitability_scores = (latest_data['ROE'] * 100).clip(0, 30) / 30 * 100
        solvency_scores = (2 - latest_data['资产负债率']).clip(0, 1) * 100
        operational_scores = [75] * len(companies)  # 基于营运周期的标准化分数
        growth_scores = (latest_data.get('资产增长率', pd.Series([0]*len(companies))) * 100 + 10).clip(0, 20) / 20 * 100

        angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
        angles += angles[:1]  # 闭合雷达图

        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']

        for i, company in enumerate(companies):
            values = [profitability_scores.iloc[i], solvency_scores.iloc[i],
                     operational_scores[i], growth_scores.iloc[i]]
            values += values[:1]  # 闭合数据

            ax1.plot(angles, values, 'o-', linewidth=2, label=company, color=colors[i])
            ax1.fill(angles, values, alpha=0.25, color=colors[i])

        ax1.set_xticks(angles[:-1])
        ax1.set_xticklabels(categories)
        ax1.set_ylim(0, 100)
        ax1.set_title('综合财务能力雷达图')
        ax1.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        ax1.grid(True)

        # 营业收入规模对比
        ax2 = axes[0, 1]
        revenue_data = latest_data['营业收入']
        bars = ax2.bar(companies, revenue_data, color=colors)
        ax2.set_title('最新年份营业收入规模对比(万元)')
        ax2.set_ylabel('营业收入(万元)')
        ax2.tick_params(axis='x', rotation=45)

        for bar, value in zip(bars, revenue_data):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(revenue_data)*0.01,
                    f'{value:.0f}万', ha='center', va='bottom', fontsize=8)

        # 净利润规模对比
        ax3 = axes[1, 0]
        profit_data = latest_data['净利润']
        bars = ax3.bar(companies, profit_data, color=colors)
        ax3.set_title('最新年份净利润规模对比(万元)')
        ax3.set_ylabel('净利润(万元)')
        ax3.tick_params(axis='x', rotation=45)

        for bar, value in zip(bars, profit_data):
            ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(profit_data)*0.01,
                    f'{value:.0f}万', ha='center', va='bottom', fontsize=8)

        # 总资产规模对比
        ax4 = axes[1, 1]
        asset_data = latest_data['总资产']
        bars = ax4.bar(companies, asset_data, color=colors)
        ax4.set_title('最新年份总资产规模对比(万元)')
        ax4.set_ylabel('总资产(万元)')
        ax4.tick_params(axis='x', rotation=45)

        for bar, value in zip(bars, asset_data):
            ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(asset_data)*0.01,
                    f'{value:.0f}万', ha='center', va='bottom', fontsize=8)

        plt.tight_layout()
        plt.savefig('综合对比分析.png', dpi=300, bbox_inches='tight')
        plt.show()





# 创建分析器实例
analyzer = FinancialAnalyzer("4390595892600087646")

# 执行完整分析
if __name__ == "__main__":
    print("=== 开始完整财务分析 ===")
    analyzer.load_data()
    analyzer.explore_data_structure()
    analyzer.clean_and_prepare_data()
    analyzer.examine_sample_data()
    analyzer.extract_financial_indicators()
    analyzer.calculate_growth_rates()
    analyzer.create_summary_dataframe()
    analyzer.create_pivot_tables()
    analyzer.create_four_abilities_pivot_tables()
    analyzer.create_visualizations()

    print("\n=== 分析完成 ===")
    print("生成的文件：")
    print("1. 财务指标汇总表.xlsx - 详细财务数据")
    print("2. 财务分析数据透视表.xlsx - 数据透视表分析")
    print("3. 四个能力数据透视表.xlsx - 四个能力专门透视表")
    print("4. 盈利能力分析.png - 盈利能力图表")
    print("5. 偿债能力分析.png - 偿债能力图表")
    print("6. 营运能力分析.png - 营运能力图表")
    print("7. 发展能力分析.png - 发展能力图表")
    print("8. 综合对比分析.png - 综合对比图表")
