# 四家环保公司财务分析系统

## 项目名称
**四家环保公司财务分析系统 (Financial Analysis System for Four Environmental Companies)**

## 任务描述

### 1. 数据集选取说明
**数据来源**：选择东方财富网作为主要数据源
**行业选择**：ESG环保行业（第二选择）
**选取原因**：
- 环保行业是国家政策重点支持的绿色发展领域
- ESG（环境、社会、治理）投资理念日益受到重视
- 行业数据相对完整，便于进行财务分析对比

**具体公司选择**：
- 上海环境公司：固废处理龙头企业
- 远达环保：大气治理领域领先企业
- 首创环保：水务环保综合服务商
- 龙净环保：大气污染治理设备制造商

**数据存储方式**：
- 将财务数据保存在Excel文件中，分别为利润表、资产负债表、现金流量表
- 使用pandas的read_excel()方法读取数据
- 通过索引和列表操作提取关键财务指标

### 2. 计算指标说明

#### 2.1 企业价值与财务绩效指标
**盈利能力指标**：
- 净利润率 = NI/sale（净利润/营业收入）
- ROA = NI/A（净利润/总资产）
- ROE = NI/E（净利润/股东权益）
- 使用zip(a,b)函数进行对应相除计算

**偿债能力指标**：
- 流动比率 = 流动资产/流动负债（对比行业平均值）
- 速动比率 = 速动资产/流动负债（速动资产=流动资产-存货-应收账款）

**营运能力指标**：
- 应收账款周转天数 = 63天
- 应付账款周转天数 = 90天
- 存货周转天数 = 40天
- 营运周期 = 63+40-90 = 13天（越短越好）

**发展能力指标**：
- 资产增长率 = 资产增长量/上期资产存量
- 示例：2024年初资产1000万，年末980万，增长率=(-20)/1000=-2%

#### 2.2 数据清洗处理
- 检查数据是否有空值（使用isnull()方法）
- 对空值使用fillna()方法进行替代处理
- 数值列填充0，文本列填充'未知'
- 统一数据格式，处理中文数值单位（亿、万等）

#### 2.3 审计匹配性分析
- 检查营业收入与营业成本的匹配性
- 识别异常情况：如营业收入增长200%但成本反而减少的不合理现象
- 验证资产负债表与利润表项目的逻辑一致性

### 3. 分类汇总与数据透视表分析

#### 3.1 分类汇总类别
**按公司分类**：
- 上海环境公司财务指标汇总
- 远达环保财务指标汇总
- 首创环保财务指标汇总
- 龙净环保财务指标汇总

**按年份分类**：
- 2020-2024年各年度财务表现
- 年度间增长率变化趋势

**按能力维度分类**：
- 盈利能力数据汇总
- 偿债能力数据汇总
- 营运能力数据汇总
- 发展能力数据汇总

#### 3.2 数据透视表分析
使用pandas的pivot_table()功能创建多维度透视表：
- 以公司为行索引，年份为列索引的财务指标透视表
- 各项财务比率的统计汇总（均值、标准差、最值）
- 综合评分排名表
- 使用to_excel()方法保存为Excel文件

### 4. 可视化图形说明

#### 4.1 完成的可视化图表类型
**趋势分析图**：
- 盈利能力趋势图（净利润率、ROA、ROE的时间序列图）
- 偿债能力趋势图（流动比率、速动比率、资产负债率变化）
- 发展能力趋势图（各类增长率的年度变化）

**对比分析图**：
- 四家公司盈利能力柱状图对比
- 营运能力指标柱状图对比（周转天数等）
- 最新年份各项指标的横向对比

**综合分析图**：
- 四维雷达图（盈利、偿债、营运、发展能力综合展示）
- 资产负债率分布饼图
- 营业收入和净利润规模对比图

**特色图表**：
- 营运周期可视化展示
- 综合评分排名图
- 投资建议等级分布图

#### 4.2 图表技术特点
- 解决matplotlib中文字体显示问题
- 使用专业的财务分析配色方案
- 添加数据标签和参考线
- 支持高清PNG格式输出

### 核心分析指标体系

#### 盈利能力指标（使用拼音变量）
```python
# 净利润率计算
jinglirunlv = NI / sale  # 净利润/营业收入

# 资产收益率计算
ROA = NI / A  # 净利润/总资产

# 净资产收益率计算
ROE = NI / E  # 净利润/股东权益

# 使用zip函数进行批量计算
for ni, sale in zip(净利润列表, 营业收入列表):
    利润率 = ni / sale if sale != 0 else 0
```

#### 偿债能力指标
```python
# 流动比率（对比行业平均值）
liudongbilv = 流动资产 / 流动负债

# 速动比率计算
速动资产 = 流动资产 - 存货 - 应收账款
sudongbilv = 速动资产 / 流动负债

# 资产负债率
zichanfuzhailu = 总负债 / 总资产
```

#### 营运能力指标（行业标准值）
```python
# 固定的行业标准天数
yingshouzhangkuan_days = 63  # 应收账款周转天数
yingfuzhangkuan_days = 90   # 应付账款周转天数
cunhuo_days = 40            # 存货周转天数

# 营运周期计算
yunying_cycle = yingshouzhangkuan_days + cunhuo_days - yingfuzhangkuan_days
# 结果：63 + 40 - 90 = 13天（越短越好）
```

#### 发展能力指标（增长率计算）
```python
# 资产增长率示例
# 2024年初：1000万，年末：980万
zichan_2024_chu = 1000  # 万元
zichan_2024_mo = 980    # 万元
zichan_zengzhanglv = (zichan_2024_mo - zichan_2024_chu) / zichan_2024_chu
# 结果：(980-1000)/1000 = -2%

# 营业收入增长率
yingyeshouru_zengzhanglv = (本期营业收入 - 上期营业收入) / 上期营业收入

# 净利润增长率
jinglirun_zengzhanglv = (本期净利润 - 上期净利润) / 上期净利润
```

## 操作步骤/思路

### 第一阶段：环境准备与数据加载
1. **环境配置**
   - 导入必要的Python库（pandas, numpy, matplotlib等）
   - 配置中文字体显示（解决matplotlib中文显示问题）
   - 设置图表样式和参数

2. **数据加载**
   - 读取四家公司的Excel财务报表文件
   - 加载利润表、资产负债表、现金流量表数据
   - 验证数据完整性和格式正确性

### 第二阶段：数据预处理
1. **数据探索**
   - 查看数据结构和维度
   - 检查数据类型和格式
   - 识别缺失值和异常值

2. **数据清洗**
   - 处理空值（数值列填充0，文本列填充'未知'）
   - 统一数据格式和单位
   - 处理中文数值格式（亿、万等单位转换）

3. **数据标准化**
   - 将所有金额统一为万元单位
   - 标准化年份格式
   - 确保数据类型一致性

### 第三阶段：财务指标计算
1. **基础指标提取**
   - 从利润表提取：营业收入、净利润、营业成本
   - 从资产负债表提取：总资产、总负债、股东权益、流动资产、流动负债等
   - 从现金流量表提取相关指标

2. **财务比率计算**
   - 计算盈利能力指标（净利润率、ROA、ROE）
   - 计算偿债能力指标（流动比率、速动比率、资产负债率）
   - 计算营运能力指标（基于行业标准）

3. **增长率计算**
   - 计算年度间的资产增长率
   - 计算营业收入增长率
   - 计算净利润增长率

### 第四阶段：数据分析与可视化
1. **创建汇总数据表**
   - 整合所有财务指标到统一数据框
   - 按公司和年份组织数据
   - 保存为Excel格式便于查看

2. **生成数据透视表**
   - 创建各维度能力的透视表分析
   - 生成统计汇总（均值、标准差、最值等）
   - 创建综合评分排名表

3. **可视化图表生成**
   - 盈利能力分析图表（趋势图、对比图）
   - 偿债能力分析图表（趋势图、饼图）
   - 营运能力分析图表（柱状图对比）
   - 发展能力分析图表（增长率趋势）
   - 综合对比分析图表（雷达图、规模对比）

### 第五阶段：文件输出与管理
1. **图表输出**
   - 保存所有图表为高清PNG格式
   - 确保中文字体正确显示
   - 使用专业的财务分析配色方案

2. **数据文件输出**
   - 导出Excel数据透视表
   - 保存财务指标汇总表
   - 生成四个能力专门透视表

### 第六阶段：结果验证与优化
1. **数据验证**
   - 检查计算结果的合理性
   - 验证图表显示的准确性
   - 确认中文字体正常显示

2. **结果解读**
   - 分析各公司财务表现特点
   - 识别优势和劣势
   - 提供具体的投资建议

## 技术特点
- **自动化处理**：一键完成从数据加载到报告生成的全流程
- **中文支持**：完美解决matplotlib中文字体显示问题
- **多维度分析**：涵盖财务分析的四个核心维度
- **可视化丰富**：提供多种图表类型展示分析结果
- **标准化输出**：生成标准格式的Excel表格和分析报告

## 核心功能模块

### 1. 数据处理模块
- **FinancialAnalyzer类**：核心分析器类
- **load_data()方法**：自动加载四家公司的财务数据
- **clean_and_prepare_data()方法**：数据清洗和预处理
- **convert_to_number()方法**：中文数值格式转换

### 2. 指标计算模块
- **extract_financial_indicators()方法**：提取和计算基础财务指标
- **calculate_growth_rates()方法**：计算各类增长率指标
- **get_value_by_keyword()方法**：智能关键词匹配提取数据

### 3. 数据分析模块
- **create_summary_dataframe()方法**：创建汇总数据表
- **create_pivot_tables()方法**：生成数据透视表
- **create_four_abilities_pivot_tables()方法**：专门的四能力透视表

### 4. 可视化模块
- **plot_profitability_analysis()方法**：盈利能力可视化
- **plot_solvency_analysis()方法**：偿债能力可视化
- **plot_operational_analysis()方法**：营运能力可视化
- **plot_growth_analysis()方法**：发展能力可视化
- **plot_comprehensive_comparison()方法**：综合对比可视化

### 5. 字体配置模块
- **ensure_chinese_font()方法**：确保中文字体正确显示
- **configure_chinese_font()方法**：配置最佳中文字体
- **find_best_chinese_font()方法**：查找系统最佳中文字体

## 输出文件说明

### Excel文件
1. **财务指标汇总表.xlsx**
   - 包含所有公司所有年份的详细财务指标
   - 按公司和年份组织的完整数据集

2. **财务分析数据透视表.xlsx**
   - 多个工作表的透视表分析
   - 包含盈利能力、偿债能力、发展能力、规模对比等透视表

3. **四个能力数据透视表.xlsx**
   - 专门针对四个能力维度的详细透视表
   - 包含原始数据、统计汇总、综合评分等工作表

### 图表文件
1. **盈利能力分析.png**：净利润率、ROA、ROE趋势分析和对比
2. **偿债能力分析.png**：流动比率、速动比率、资产负债率分析
3. **营运能力分析.png**：营运周期、周转天数等指标对比
4. **发展能力分析.png**：各类增长率趋势和对比分析
5. **综合对比分析.png**：雷达图和规模对比的综合分析



## 使用方法

### 环境要求
```python
# 必需的Python库
pandas >= 1.3.0
numpy >= 1.21.0
matplotlib >= 3.4.0
openpyxl >= 3.0.0
```

### 数据准备
1. 将四家公司的财务报表Excel文件放在指定文件夹中
2. 确保文件命名格式正确：
   - `公司名--利润表.xlsx`
   - `公司名--资产负债表.xlsx`
   - `公司名--现金流量表.xlsx`

### 运行程序
```bash
python financial_analysis.py
```

### 字体配置（Mac系统）
如果遇到中文显示问题，可以运行：
```bash
python fix_chinese_font.py
```

## 分析结果解读

### 综合评分体系
- **盈利能力得分**：基于ROE指标，满分25分
- **偿债能力得分**：基于资产负债率，满分25分
- **营运能力得分**：基于营运周期，满分25分
- **发展能力得分**：基于资产增长率，满分25分
- **总分**：四项得分之和，满分100分

### 投资建议标准
- **90分以上**：A+级，强烈推荐投资
- **80-90分**：A级，推荐投资
- **70-80分**：B+级，谨慎投资
- **60-70分**：B级，不推荐投资
- **60分以下**：C级，避免投资
