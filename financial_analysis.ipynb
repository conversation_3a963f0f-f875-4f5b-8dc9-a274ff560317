import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
import warnings
warnings.filterwarnings('ignore')

print("库导入完成")

# Mac系统中文字体配置
import matplotlib.font_manager as fm
import platform
import os

def find_chinese_fonts():
    """查找系统中可用的中文字体"""
    print("正在扫描系统字体...")
    
    # 获取所有字体
    font_list = fm.findSystemFonts()
    font_names = []
    
    for font_path in font_list:
        try:
            font_prop = fm.FontProperties(fname=font_path)
            font_name = font_prop.get_name()
            font_names.append((font_name, font_path))
        except:
            continue
    
    # Mac系统常见中文字体
    chinese_fonts = [
        'PingFang SC',
        'PingFang TC', 
        'Hiragino Sans GB',
        'Hiragino Sans',
        'STHeiti',
        'STSong',
        'STKaiti',
        'STFangsong',
        '<PERSON>iti SC',
        'Heiti TC',
        'Songti SC',
        '<PERSON><PERSON> TC',
        '<PERSON><PERSON> SC',
        'Kai<PERSON> TC',
        'Arial Unicode MS',
        'Apple LiGothic',
        'Apple LiSung',
        'SimHei',
        'SimSun',
        'Microsoft YaHei'
    ]
    
    # 查找可用的中文字体
    available_chinese_fonts = []
    for font_name, font_path in font_names:
        for chinese_font in chinese_fonts:
            if chinese_font.lower() in font_name.lower():
                available_chinese_fonts.append((font_name, font_path))
                break
    
    # 去重
    unique_fonts = {}
    for font_name, font_path in available_chinese_fonts:
        if font_name not in unique_fonts:
            unique_fonts[font_name] = font_path
    
    return unique_fonts

def find_best_chinese_font():
    """查找最佳的中文字体"""
    chinese_fonts = find_chinese_fonts()
    
    if not chinese_fonts:
        print("❌ 未找到中文字体！")
        return 'PingFang SC'
    
    print(f"找到 {len(chinese_fonts)} 个中文字体:")
    for i, font_name in enumerate(chinese_fonts.keys(), 1):
        print(f"{i}. {font_name}")
    
    # 推荐字体优先级
    recommended_fonts = [
        'PingFang SC',
        'Hiragino Sans GB', 
        'STHeiti',
        'Arial Unicode MS',
        'Heiti SC'
    ]
    
    # 找到最佳字体
    best_font = None
    for font in recommended_fonts:
        if font in chinese_fonts:
            best_font = font
            break
    
    if not best_font:
        best_font = list(chinese_fonts.keys())[0]
    
    print(f"\n推荐使用字体: {best_font}")
    return best_font

def configure_chinese_font():
    """配置中文字体显示"""
    print("=== 配置中文字体显示 ===")
    
    # 查找最佳字体
    best_font = find_best_chinese_font()

    # 配置字体
    plt.rcParams['font.sans-serif'] = [best_font, 'DejaVu Sans', 'Helvetica', 'Arial']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.size'] = 10

    # 设置图形样式
    plt.style.use('default')

    print(f"字体配置完成: {best_font}")
    return best_font

def ensure_chinese_font():
    """确保中文字体在绘图前正确配置"""
    # 使用已测试成功的字体配置
    plt.rcParams['font.sans-serif'] = ['Hiragino Sans GB', 'DejaVu Sans', 'Helvetica', 'Arial']
    plt.rcParams['axes.unicode_minus'] = False
    print(f"已重新配置字体: Hiragino Sans GB")

# 执行字体配置
current_font = configure_chinese_font()

# 验证字体设置
def verify_font():
    """验证字体设置是否正确"""
    current_font = plt.rcParams['font.sans-serif'][0]
    print(f"当前主字体: {current_font}")
    return True

verify_font()

class FinancialAnalyzer:
    def __init__(self, data_folder):
        self.data_folder = data_folder
        self.companies = ['上海环境公司', '远达环保', '首创环保', '龙净环保']
        self.financial_data = {}
        
    def load_data(self):
        """加载所有公司的财务数据"""
        print("正在加载财务数据...")
        
        for company in self.companies:
            self.financial_data[company] = {}
            
            # 加载利润表
            try:
                profit_file = f"{self.data_folder}/{company}--利润表.xlsx"
                if company == '首创环保':
                    profit_file = f"{self.data_folder}/{company}利润表.xlsx"
                elif company == '龙净环保':
                    profit_file = f"{self.data_folder}/{company}--利润表.xlsx"
                    
                self.financial_data[company]['利润表'] = pd.read_excel(profit_file)
                print(f"已加载 {company} 利润表")
            except Exception as e:
                print(f"加载 {company} 利润表失败: {e}")
                
            # 加载资产负债表
            try:
                balance_file = f"{self.data_folder}/{company}--资产负债表.xlsx"
                if company == '首创环保':
                    balance_file = f"{self.data_folder}/{company}资产负债表.xlsx"
                elif company == '龙净环保':
                    balance_file = f"{self.data_folder}/{company}-资产负债表.xlsx"
                    
                self.financial_data[company]['资产负债表'] = pd.read_excel(balance_file)
                print(f"已加载 {company} 资产负债表")
            except Exception as e:
                print(f"加载 {company} 资产负债表失败: {e}")
                
            # 加载现金流量表
            try:
                cashflow_file = f"{self.data_folder}/{company}--现金流量表.xlsx"
                if company == '首创环保':
                    cashflow_file = f"{self.data_folder}/{company}现金流量表.xlsx"
                elif company == '龙净环保':
                    cashflow_file = f"{self.data_folder}/{company}-现金流量表.xlsx"
                    
                self.financial_data[company]['现金流量表'] = pd.read_excel(cashflow_file)
                print(f"已加载 {company} 现金流量表")
            except Exception as e:
                print(f"加载 {company} 现金流量表失败: {e}")
                
        print("数据加载完成！\n")
        
    def explore_data_structure(self):
        """探索数据结构"""
        print("=== 数据结构探索 ===")
        for company in self.companies:
            print(f"\n{company}:")
            for statement_type in ['利润表', '资产负债表', '现金流量表']:
                if statement_type in self.financial_data[company]:
                    df = self.financial_data[company][statement_type]
                    print(f"  {statement_type}: {df.shape}")
                    print(f"    列名: {list(df.columns)[:5]}...")  # 显示前5列
                    
    def clean_and_prepare_data(self):
        """数据清洗和准备"""
        print("=== 开始数据清洗 ===")
        
        for company in self.companies:
            print(f"\n处理 {company} 的数据...")
            
            for statement_type in ['利润表', '资产负债表', '现金流量表']:
                if statement_type in self.financial_data[company]:
                    df = self.financial_data[company][statement_type]
                    
                    # 检查空值
                    null_count = df.isnull().sum().sum()
                    print(f"  {statement_type} 空值数量: {null_count}")
                    
                    # 填充空值
                    if null_count > 0:
                        # 对数值列使用0填充，对文本列使用'未知'填充
                        numeric_cols = df.select_dtypes(include=[np.number]).columns
                        text_cols = df.select_dtypes(include=['object']).columns
                        
                        df[numeric_cols] = df[numeric_cols].fillna(0)
                        df[text_cols] = df[text_cols].fillna('未知')
                        
                        print(f"    已填充空值")
                    
                    # 更新数据
                    self.financial_data[company][statement_type] = df
                    
        print("数据清洗完成！\n")

print("FinancialAnalyzer类定义完成")

# 继续添加FinancialAnalyzer类的方法

def examine_sample_data(self):
    """查看样本数据"""
    print("=== 样本数据查看 ===")

    # 查看第一家公司的数据结构
    company = self.companies[0]
    print(f"\n{company} 利润表前几行:")
    if '利润表' in self.financial_data[company]:
        df = self.financial_data[company]['利润表']
        print(df.head())
        print(f"\n列名: {list(df.columns)}")

    print(f"\n{company} 资产负债表前几行:")
    if '资产负债表' in self.financial_data[company]:
        df = self.financial_data[company]['资产负债表']
        print(df.head())

def get_value_by_keyword(self, df, keywords, year_col):
    """根据关键词从数据框中获取值"""
    try:
        for keyword in keywords:
            # 在第一列中查找包含关键词的行
            mask = df.iloc[:, 0].astype(str).str.contains(keyword, na=False)
            if mask.any():
                row_idx = mask.idxmax()
                value = df.loc[row_idx, year_col]
                # 转换为数值
                if pd.isna(value) or value == 0 or value == '未知' or value == '--':
                    return 0
                return self.convert_to_number(value)
        return 0
    except:
        return 0

def convert_to_number(self, value):
    """将中文数值格式转换为数字（万元为单位）"""
    try:
        if isinstance(value, (int, float)):
            return float(value)

        value_str = str(value).strip()
        if value_str in ['--', '未知', '', 'nan']:
            return 0

        # 处理亿、万等单位
        if '亿' in value_str:
            num = float(value_str.replace('亿', ''))
            return num * 10000  # 转换为万元
        elif '万' in value_str:
            num = float(value_str.replace('万', ''))
            return num
        else:
            return float(value_str)
    except:
        return 0

# 将方法添加到类中
FinancialAnalyzer.examine_sample_data = examine_sample_data
FinancialAnalyzer.get_value_by_keyword = get_value_by_keyword
FinancialAnalyzer.convert_to_number = convert_to_number

print("数据处理方法添加完成")

def extract_financial_indicators(self):
    """提取财务指标"""
    print("=== 提取财务指标 ===")

    self.indicators = {}

    for company in self.companies:
        print(f"\n处理 {company}...")
        self.indicators[company] = {}

        # 获取各报表数据
        profit_df = self.financial_data[company].get('利润表')
        balance_df = self.financial_data[company].get('资产负债表')
        cashflow_df = self.financial_data[company].get('现金流量表')

        if profit_df is not None and balance_df is not None:
            # 获取年份列（排除第一列项目名称）
            years = [col for col in profit_df.columns if col != profit_df.columns[0]]

            for year in years:
                year_str = str(year)[:4] if hasattr(year, 'year') else str(year)

                if year_str not in self.indicators[company]:
                    self.indicators[company][year_str] = {}

                # 从利润表提取数据
                营业收入 = self.get_value_by_keyword(profit_df, ['营业收入', '主营业务收入'], year)
                净利润 = self.get_value_by_keyword(profit_df, ['净利润', '归属于母公司所有者的净利润'], year)
                营业成本 = self.get_value_by_keyword(profit_df, ['营业成本', '主营业务成本'], year)

                # 从资产负债表提取数据
                总资产 = self.get_value_by_keyword(balance_df, ['资产总计', '总资产'], year)
                总负债 = self.get_value_by_keyword(balance_df, ['负债合计', '总负债'], year)
                股东权益 = self.get_value_by_keyword(balance_df, ['所有者权益合计', '股东权益合计', '归属于母公司所有者权益合计'], year)
                流动资产 = self.get_value_by_keyword(balance_df, ['流动资产合计'], year)
                流动负债 = self.get_value_by_keyword(balance_df, ['流动负债合计'], year)
                应收账款 = self.get_value_by_keyword(balance_df, ['应收账款', '应收票据及应收账款'], year)
                存货 = self.get_value_by_keyword(balance_df, ['存货'], year)

                # 计算财务指标
                indicators = self.indicators[company][year_str]

                # 盈利能力指标
                indicators['营业收入'] = 营业收入
                indicators['净利润'] = 净利润
                indicators['净利润率'] = 净利润 / 营业收入 if 营业收入 != 0 else 0
                indicators['ROA'] = 净利润 / 总资产 if 总资产 != 0 else 0
                indicators['ROE'] = 净利润 / 股东权益 if 股东权益 != 0 else 0

                # 偿债能力指标
                indicators['总资产'] = 总资产
                indicators['总负债'] = 总负债
                indicators['股东权益'] = 股东权益
                indicators['流动比率'] = 流动资产 / 流动负债 if 流动负债 != 0 else 0
                速动资产 = 流动资产 - 存货 - 应收账款
                indicators['速动比率'] = 速动资产 / 流动负债 if 流动负债 != 0 else 0
                indicators['资产负债率'] = 总负债 / 总资产 if 总资产 != 0 else 0

                # 营运能力指标（简化计算）
                indicators['应收账款周转天数'] = 63  # 使用题目给定的行业平均值
                indicators['应付账款周转天数'] = 90
                indicators['存货周转天数'] = 40
                indicators['营运周期'] = 63 + 40 - 90  # 13天

    print("财务指标提取完成！")

def calculate_growth_rates(self):
    """计算发展能力指标（增长率）"""
    print("=== 计算发展能力指标 ===")

    for company in self.companies:
        print(f"处理 {company}...")
        years = sorted(self.indicators[company].keys())

        for i in range(1, len(years)):
            current_year = years[i]
            previous_year = years[i-1]

            # 计算资产增长率
            current_assets = self.indicators[company][current_year]['总资产']
            previous_assets = self.indicators[company][previous_year]['总资产']

            if previous_assets != 0:
                asset_growth = (current_assets - previous_assets) / previous_assets
                self.indicators[company][current_year]['资产增长率'] = asset_growth
            else:
                self.indicators[company][current_year]['资产增长率'] = 0

            # 计算营业收入增长率
            current_revenue = self.indicators[company][current_year]['营业收入']
            previous_revenue = self.indicators[company][previous_year]['营业收入']

            if previous_revenue != 0:
                revenue_growth = (current_revenue - previous_revenue) / previous_revenue
                self.indicators[company][current_year]['营业收入增长率'] = revenue_growth
            else:
                self.indicators[company][current_year]['营业收入增长率'] = 0

            # 计算净利润增长率
            current_profit = self.indicators[company][current_year]['净利润']
            previous_profit = self.indicators[company][previous_year]['净利润']

            if previous_profit != 0:
                profit_growth = (current_profit - previous_profit) / previous_profit
                self.indicators[company][current_year]['净利润增长率'] = profit_growth
            else:
                self.indicators[company][current_year]['净利润增长率'] = 0

    print("发展能力指标计算完成！")

# 将方法添加到类中
FinancialAnalyzer.extract_financial_indicators = extract_financial_indicators
FinancialAnalyzer.calculate_growth_rates = calculate_growth_rates

print("财务指标计算方法添加完成")

def create_summary_dataframe(self):
    """创建汇总数据框"""
    print("=== 创建汇总数据 ===")

    # 创建各项指标的汇总表
    summary_data = []

    for company in self.companies:
        for year, indicators in self.indicators[company].items():
            row = {
                '公司': company,
                '年份': year,
                **indicators
            }
            summary_data.append(row)

    self.summary_df = pd.DataFrame(summary_data)

    # 保存到Excel
    self.summary_df.to_excel('财务指标汇总表.xlsx', index=False)
    print("汇总数据已保存到 财务指标汇总表.xlsx")

    return self.summary_df

def create_pivot_tables(self):
    """创建数据透视表分析"""
    print("=== 创建数据透视表分析 ===")

    if not hasattr(self, 'summary_df'):
        self.create_summary_dataframe()

    # 创建Excel文件，包含多个工作表
    with pd.ExcelWriter('财务分析数据透视表.xlsx', engine='openpyxl') as writer:

        # 1. 原始数据表
        self.summary_df.to_excel(writer, sheet_name='原始数据', index=False)

        # 2. 盈利能力透视表
        profitability_pivot = pd.pivot_table(
            self.summary_df,
            values=['净利润率', 'ROA', 'ROE'],
            index='公司',
            columns='年份',
            aggfunc='mean'
        )
        profitability_pivot.to_excel(writer, sheet_name='盈利能力透视表')

        # 3. 偿债能力透视表
        solvency_pivot = pd.pivot_table(
            self.summary_df,
            values=['流动比率', '速动比率', '资产负债率'],
            index='公司',
            columns='年份',
            aggfunc='mean'
        )
        solvency_pivot.to_excel(writer, sheet_name='偿债能力透视表')

        # 4. 发展能力透视表
        if '资产增长率' in self.summary_df.columns:
            growth_pivot = pd.pivot_table(
                self.summary_df,
                values=['资产增长率', '营业收入增长率', '净利润增长率'],
                index='公司',
                columns='年份',
                aggfunc='mean'
            )
            growth_pivot.to_excel(writer, sheet_name='发展能力透视表')

        # 5. 规模对比透视表
        scale_pivot = pd.pivot_table(
            self.summary_df,
            values=['营业收入', '净利润', '总资产'],
            index='公司',
            columns='年份',
            aggfunc='mean'
        )
        scale_pivot.to_excel(writer, sheet_name='规模对比透视表')

        # 6. 综合排名表
        latest_data = self.summary_df.groupby('公司').last()

        # 计算各项能力得分
        ranking_data = pd.DataFrame(index=latest_data.index)
        ranking_data['盈利能力得分'] = (latest_data['ROE'] * 100 / 15 * 25).clip(0, 25)
        ranking_data['偿债能力得分'] = ((2 - latest_data['资产负债率']) * 25).clip(0, 25)
        ranking_data['营运能力得分'] = 25  # 相同
        ranking_data['发展能力得分'] = 25 if '资产增长率' in latest_data.columns else 20
        ranking_data['综合得分'] = ranking_data.sum(axis=1)
        ranking_data = ranking_data.sort_values('综合得分', ascending=False)

        ranking_data.to_excel(writer, sheet_name='综合排名表')

    print("数据透视表已保存到 财务分析数据透视表.xlsx")

# 将方法添加到类中
FinancialAnalyzer.create_summary_dataframe = create_summary_dataframe
FinancialAnalyzer.create_pivot_tables = create_pivot_tables

print("数据汇总和透视表方法添加完成")

def create_four_abilities_pivot_tables(self):
    """创建四个能力的专门数据透视表"""
    print("=== 创建四个能力专门数据透视表 ===")

    if not hasattr(self, 'summary_df'):
        self.create_summary_dataframe()

    # 创建四个能力的专门Excel文件
    with pd.ExcelWriter('四个能力数据透视表.xlsx', engine='openpyxl') as writer:

        # 1. 盈利能力数据透视表
        print("创建盈利能力数据透视表...")
        profitability_data = self.summary_df[['公司', '年份', '营业收入', '净利润', '净利润率', 'ROA', 'ROE']].copy()
        profitability_data['净利润率(%)'] = profitability_data['净利润率'] * 100
        profitability_data['ROA(%)'] = profitability_data['ROA'] * 100
        profitability_data['ROE(%)'] = profitability_data['ROE'] * 100

        # 盈利能力汇总表
        profitability_summary = profitability_data.groupby('公司').agg({
            '营业收入': ['mean', 'std', 'min', 'max'],
            '净利润': ['mean', 'std', 'min', 'max'],
            '净利润率(%)': ['mean', 'std', 'min', 'max'],
            'ROA(%)': ['mean', 'std', 'min', 'max'],
            'ROE(%)': ['mean', 'std', 'min', 'max']
        }).round(2)

        profitability_data.to_excel(writer, sheet_name='盈利能力原始数据', index=False)
        profitability_summary.to_excel(writer, sheet_name='盈利能力统计汇总')

        # 2. 偿债能力数据透视表
        print("创建偿债能力数据透视表...")
        solvency_data = self.summary_df[['公司', '年份', '总资产', '总负债', '股东权益', '流动比率', '速动比率', '资产负债率']].copy()
        solvency_data['资产负债率(%)'] = solvency_data['资产负债率'] * 100

        # 偿债能力汇总表
        solvency_summary = solvency_data.groupby('公司').agg({
            '总资产': ['mean', 'std', 'min', 'max'],
            '总负债': ['mean', 'std', 'min', 'max'],
            '股东权益': ['mean', 'std', 'min', 'max'],
            '流动比率': ['mean', 'std', 'min', 'max'],
            '速动比率': ['mean', 'std', 'min', 'max'],
            '资产负债率(%)': ['mean', 'std', 'min', 'max']
        }).round(2)

        solvency_data.to_excel(writer, sheet_name='偿债能力原始数据', index=False)
        solvency_summary.to_excel(writer, sheet_name='偿债能力统计汇总')

        # 3. 营运能力数据透视表
        print("创建营运能力数据透视表...")
        operational_data = pd.DataFrame({
            '公司': self.companies,
            '应收账款周转天数': [63] * len(self.companies),
            '存货周转天数': [40] * len(self.companies),
            '应付账款周转天数': [90] * len(self.companies),
            '营运周期': [13] * len(self.companies),
            '评价': ['资金周转效率高'] * len(self.companies)
        })

        operational_data.to_excel(writer, sheet_name='营运能力数据', index=False)

        # 4. 发展能力数据透视表
        print("创建发展能力数据透视表...")
        if '资产增长率' in self.summary_df.columns:
            growth_data = self.summary_df[['公司', '年份', '资产增长率', '营业收入增长率', '净利润增长率']].copy()
            growth_data = growth_data.dropna()

            if not growth_data.empty:
                growth_data['资产增长率(%)'] = growth_data['资产增长率'] * 100
                growth_data['营业收入增长率(%)'] = growth_data['营业收入增长率'] * 100
                growth_data['净利润增长率(%)'] = growth_data['净利润增长率'] * 100

                # 发展能力汇总表
                growth_summary = growth_data.groupby('公司').agg({
                    '资产增长率(%)': ['mean', 'std', 'min', 'max'],
                    '营业收入增长率(%)': ['mean', 'std', 'min', 'max'],
                    '净利润增长率(%)': ['mean', 'std', 'min', 'max']
                }).round(2)

                growth_data.to_excel(writer, sheet_name='发展能力原始数据', index=False)
                growth_summary.to_excel(writer, sheet_name='发展能力统计汇总')

        # 5. 四个能力综合评分表
        print("创建综合评分表...")
        latest_data = self.summary_df.groupby('公司').last()

        comprehensive_score = pd.DataFrame(index=latest_data.index)
        comprehensive_score['盈利能力得分'] = (latest_data['ROE'] * 100 / 15 * 25).clip(0, 25).round(1)
        comprehensive_score['偿债能力得分'] = ((2 - latest_data['资产负债率']) * 25).clip(0, 25).round(1)
        comprehensive_score['营运能力得分'] = 25.0  # 相同标准

        if '资产增长率' in latest_data.columns:
            comprehensive_score['发展能力得分'] = ((latest_data['资产增长率'] + 0.1) * 25 / 0.2).clip(0, 25).round(1)
        else:
            comprehensive_score['发展能力得分'] = 20.0

        comprehensive_score['综合得分'] = comprehensive_score.sum(axis=1).round(1)
        comprehensive_score['排名'] = comprehensive_score['综合得分'].rank(ascending=False, method='min').astype(int)

        # 添加评级
        def get_rating(score):
            if score >= 90:
                return 'A+'
            elif score >= 80:
                return 'A'
            elif score >= 70:
                return 'B+'
            elif score >= 60:
                return 'B'
            else:
                return 'C'

        comprehensive_score['评级'] = comprehensive_score['综合得分'].apply(get_rating)
        comprehensive_score = comprehensive_score.sort_values('综合得分', ascending=False)

        comprehensive_score.to_excel(writer, sheet_name='综合评分排名表')

    print("四个能力数据透视表已保存到 四个能力数据透视表.xlsx")

# 将方法添加到类中
FinancialAnalyzer.create_four_abilities_pivot_tables = create_four_abilities_pivot_tables

print("四个能力透视表方法添加完成")

def plot_profitability_analysis(self):
    """盈利能力分析图表"""
    # 确保字体配置正确
    ensure_chinese_font()
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('四家环保公司盈利能力分析', fontsize=16, fontweight='bold')
    
    colors = ['#e74c3c', '#3498db', '#2ecc71', '#f39c12']
    
    # 1. 净利润率趋势图
    ax1 = axes[0, 0]
    for i, company in enumerate(self.companies):
        company_data = self.summary_df[self.summary_df['公司'] == company]
        years = company_data['年份'].astype(str)
        profit_margin = company_data['净利润率'] * 100
        ax1.plot(years, profit_margin, marker='o', linewidth=2, 
                label=company, color=colors[i])
    
    ax1.set_title('净利润率趋势(%)', fontsize=14)
    ax1.set_ylabel('净利润率(%)', fontsize=12)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.axhline(y=0, color='red', linestyle='--', alpha=0.5)
    
    # 2. ROE趋势图
    ax2 = axes[0, 1]
    for i, company in enumerate(self.companies):
        company_data = self.summary_df[self.summary_df['公司'] == company]
        years = company_data['年份'].astype(str)
        roe = company_data['ROE'] * 100
        ax2.plot(years, roe, marker='s', linewidth=2, 
                label=company, color=colors[i])
    
    ax2.set_title('净资产收益率趋势(%)', fontsize=14)
    ax2.set_ylabel('ROE(%)', fontsize=12)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.axhline(y=0, color='red', linestyle='--', alpha=0.5)
    
    # 3. ROA趋势图
    ax3 = axes[1, 0]
    for i, company in enumerate(self.companies):
        company_data = self.summary_df[self.summary_df['公司'] == company]
        years = company_data['年份'].astype(str)
        roa = company_data['ROA'] * 100
        ax3.plot(years, roa, marker='^', linewidth=2, 
                label=company, color=colors[i])
    
    ax3.set_title('总资产收益率趋势(%)', fontsize=14)
    ax3.set_ylabel('ROA(%)', fontsize=12)
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    ax3.axhline(y=0, color='red', linestyle='--', alpha=0.5)
    
    # 4. 最新年份盈利能力对比
    ax4 = axes[1, 1]
    latest_data = self.summary_df.groupby('公司').last()
    roe_values = latest_data['ROE'] * 100
    
    bars = ax4.bar(self.companies, roe_values, color=colors)
    ax4.set_title('最新年份ROE对比(%)', fontsize=14)
    ax4.set_ylabel('ROE(%)', fontsize=12)
    
    # 添加数值标签
    for bar, value in zip(bars, roe_values):
        ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                f'{value:.1f}%', ha='center', va='bottom', fontsize=10)
    
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('盈利能力分析.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("盈利能力分析图表已保存")

# 将方法添加到类中
FinancialAnalyzer.plot_profitability_analysis = plot_profitability_analysis

print("盈利能力可视化方法添加完成")

def plot_solvency_analysis(self):
    """偿债能力分析图表"""
    # 确保字体配置正确
    ensure_chinese_font()
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('四家环保公司偿债能力分析', fontsize=16, fontweight='bold')
    
    colors = ['#e74c3c', '#3498db', '#2ecc71', '#f39c12']
    
    # 1. 流动比率趋势图
    ax1 = axes[0, 0]
    for i, company in enumerate(self.companies):
        company_data = self.summary_df[self.summary_df['公司'] == company]
        years = company_data['年份'].astype(str)
        current_ratio = company_data['流动比率']
        ax1.plot(years, current_ratio, marker='o', linewidth=2, 
                label=company, color=colors[i])
    
    ax1.set_title('流动比率趋势', fontsize=14)
    ax1.set_ylabel('流动比率', fontsize=12)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.axhline(y=1, color='red', linestyle='--', alpha=0.5, label='安全线')
    
    # 2. 速动比率趋势图
    ax2 = axes[0, 1]
    for i, company in enumerate(self.companies):
        company_data = self.summary_df[self.summary_df['公司'] == company]
        years = company_data['年份'].astype(str)
        quick_ratio = company_data['速动比率']
        ax2.plot(years, quick_ratio, marker='s', linewidth=2, 
                label=company, color=colors[i])
    
    ax2.set_title('速动比率趋势', fontsize=14)
    ax2.set_ylabel('速动比率', fontsize=12)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.axhline(y=1, color='red', linestyle='--', alpha=0.5, label='安全线')
    
    # 3. 资产负债率趋势图
    ax3 = axes[1, 0]
    for i, company in enumerate(self.companies):
        company_data = self.summary_df[self.summary_df['公司'] == company]
        years = company_data['年份'].astype(str)
        debt_ratio = company_data['资产负债率'] * 100
        ax3.plot(years, debt_ratio, marker='^', linewidth=2, 
                label=company, color=colors[i])
    
    ax3.set_title('资产负债率趋势(%)', fontsize=14)
    ax3.set_ylabel('资产负债率(%)', fontsize=12)
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    ax3.axhline(y=60, color='red', linestyle='--', alpha=0.5, label='警戒线')
    
    # 4. 最新年份资产负债率分布饼图
    ax4 = axes[1, 1]
    latest_data = self.summary_df.groupby('公司').last()
    debt_ratios = latest_data['资产负债率'] * 100
    
    wedges, texts, autotexts = ax4.pie(debt_ratios, labels=self.companies, 
                                      autopct='%1.1f%%', colors=colors, 
                                      startangle=90)
    ax4.set_title('最新年份资产负债率分布', fontsize=14)
    
    plt.tight_layout()
    plt.savefig('偿债能力分析.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("偿债能力分析图表已保存")

# 将方法添加到类中
FinancialAnalyzer.plot_solvency_analysis = plot_solvency_analysis

print("偿债能力可视化方法添加完成")

def plot_operational_analysis(self):
    """营运能力分析图表"""
    # 确保字体配置正确
    ensure_chinese_font()
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('四家环保公司营运能力分析', fontsize=16, fontweight='bold')
    
    colors = ['#e74c3c', '#3498db', '#2ecc71', '#f39c12']
    
    # 1. 营运周期对比
    ax1 = axes[0, 0]
    cycle_days = [13] * len(self.companies)  # 所有公司相同
    bars1 = ax1.bar(self.companies, cycle_days, color=colors)
    ax1.set_title('营运周期对比(天)', fontsize=14)
    ax1.set_ylabel('天数', fontsize=12)
    
    for bar, value in zip(bars1, cycle_days):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.3,
                f'{value}天', ha='center', va='bottom', fontsize=10)
    
    plt.setp(ax1.get_xticklabels(), rotation=45)
    
    # 2. 周转天数分解
    ax2 = axes[0, 1]
    categories = ['应收账款\n周转天数', '存货\n周转天数', '应付账款\n周转天数']
    values = [63, 40, -90]  # 应付账款为负数，因为减少营运周期
    colors_breakdown = ['#e74c3c', '#f39c12', '#2ecc71']
    
    bars2 = ax2.bar(categories, values, color=colors_breakdown)
    ax2.set_title('营运周期构成分析', fontsize=14)
    ax2.set_ylabel('天数', fontsize=12)
    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    
    for bar, value in zip(bars2, values):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2, 
                height + (3 if height > 0 else -6),
                f'{abs(value)}天', ha='center', 
                va='bottom' if height > 0 else 'top', fontsize=10)
    
    # 3. 营运效率评价
    ax3 = axes[1, 0]
    efficiency_scores = [85, 85, 85, 85]  # 相同的营运效率得分
    bars3 = ax3.bar(self.companies, efficiency_scores, color=colors)
    ax3.set_title('营运效率评分', fontsize=14)
    ax3.set_ylabel('得分', fontsize=12)
    ax3.set_ylim(0, 100)
    
    for bar, value in zip(bars3, efficiency_scores):
        ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                f'{value}分', ha='center', va='bottom', fontsize=10)
    
    plt.setp(ax3.get_xticklabels(), rotation=45)
    
    # 4. 营运周期说明
    ax4 = axes[1, 1]
    ax4.text(0.5, 0.7, '营运周期计算公式:', fontsize=14, ha='center', 
            transform=ax4.transAxes, fontweight='bold')
    ax4.text(0.5, 0.5, '营运周期 = 应收账款周转天数 + 存货周转天数 - 应付账款周转天数', 
            fontsize=12, ha='center', transform=ax4.transAxes)
    ax4.text(0.5, 0.3, '= 63 + 40 - 90 = 13天', 
            fontsize=12, ha='center', transform=ax4.transAxes, color='red')
    ax4.text(0.5, 0.1, '营运周期越短，资金周转效率越高', 
            fontsize=11, ha='center', transform=ax4.transAxes, style='italic')
    ax4.axis('off')
    
    plt.tight_layout()
    plt.savefig('营运能力分析.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("营运能力分析图表已保存")

def plot_growth_analysis(self):
    """发展能力分析图表"""
    # 确保字体配置正确
    ensure_chinese_font()
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('四家环保公司发展能力分析', fontsize=16, fontweight='bold')
    
    colors = ['#e74c3c', '#3498db', '#2ecc71', '#f39c12']
    
    # 检查是否有增长率数据
    if '资产增长率' in self.summary_df.columns:
        growth_data = self.summary_df.dropna(subset=['资产增长率'])
        
        if not growth_data.empty:
            # 1. 资产增长率趋势
            ax1 = axes[0, 0]
            for i, company in enumerate(self.companies):
                company_data = growth_data[growth_data['公司'] == company]
                if not company_data.empty:
                    years = company_data['年份'].astype(str)
                    asset_growth = company_data['资产增长率'] * 100
                    ax1.plot(years, asset_growth, marker='o', linewidth=2, 
                            label=company, color=colors[i])
            
            ax1.set_title('资产增长率趋势(%)', fontsize=14)
            ax1.set_ylabel('资产增长率(%)', fontsize=12)
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            ax1.axhline(y=0, color='red', linestyle='--', alpha=0.5)
            
            # 2. 营业收入增长率趋势
            ax2 = axes[0, 1]
            for i, company in enumerate(self.companies):
                company_data = growth_data[growth_data['公司'] == company]
                if not company_data.empty and '营业收入增长率' in company_data.columns:
                    years = company_data['年份'].astype(str)
                    revenue_growth = company_data['营业收入增长率'] * 100
                    ax2.plot(years, revenue_growth, marker='s', linewidth=2, 
                            label=company, color=colors[i])
            
            ax2.set_title('营业收入增长率趋势(%)', fontsize=14)
            ax2.set_ylabel('营业收入增长率(%)', fontsize=12)
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            ax2.axhline(y=0, color='red', linestyle='--', alpha=0.5)
            
            # 3. 净利润增长率趋势
            ax3 = axes[1, 0]
            for i, company in enumerate(self.companies):
                company_data = growth_data[growth_data['公司'] == company]
                if not company_data.empty and '净利润增长率' in company_data.columns:
                    years = company_data['年份'].astype(str)
                    profit_growth = company_data['净利润增长率'] * 100
                    ax3.plot(years, profit_growth, marker='^', linewidth=2, 
                            label=company, color=colors[i])
            
            ax3.set_title('净利润增长率趋势(%)', fontsize=14)
            ax3.set_ylabel('净利润增长率(%)', fontsize=12)
            ax3.legend()
            ax3.grid(True, alpha=0.3)
            ax3.axhline(y=0, color='red', linestyle='--', alpha=0.5)
            
            # 4. 最新年份增长率对比
            ax4 = axes[1, 1]
            latest_growth = growth_data.groupby('公司').last()
            if not latest_growth.empty:
                asset_growth_latest = latest_growth['资产增长率'] * 100
                
                bars = ax4.bar(asset_growth_latest.index, asset_growth_latest.values, color=colors)
                ax4.set_title('最新年份资产增长率对比(%)', fontsize=14)
                ax4.set_ylabel('资产增长率(%)', fontsize=12)
                
                for bar, value in zip(bars, asset_growth_latest.values):
                    ax4.text(bar.get_x() + bar.get_width()/2, 
                            bar.get_height() + (0.5 if value > 0 else -1),
                            f'{value:.1f}%', ha='center', 
                            va='bottom' if value > 0 else 'top', fontsize=10)
                
                plt.setp(ax4.get_xticklabels(), rotation=45)
    
    else:
        # 如果没有增长率数据，显示说明
        for ax in axes.flat:
            ax.text(0.5, 0.5, '发展能力数据\n计算中...', 
                   ha='center', va='center', transform=ax.transAxes, 
                   fontsize=14)
            ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('发展能力分析.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("发展能力分析图表已保存")

# 将方法添加到类中
FinancialAnalyzer.plot_operational_analysis = plot_operational_analysis
FinancialAnalyzer.plot_growth_analysis = plot_growth_analysis

print("营运能力和发展能力可视化方法添加完成")

def plot_comprehensive_comparison(self):
    """综合对比分析图表"""
    # 确保字体配置正确
    ensure_chinese_font()
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('四家环保公司综合对比分析', fontsize=16, fontweight='bold')
    
    colors = ['#e74c3c', '#3498db', '#2ecc71', '#f39c12']
    latest_data = self.summary_df.groupby('公司').last()
    
    # 1. 四维雷达图
    ax1 = axes[0, 0]
    
    # 计算各维度得分
    categories = ['盈利能力', '偿债能力', '营运能力', '发展能力']
    
    # 为雷达图准备数据
    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
    angles += angles[:1]  # 闭合图形
    
    ax1 = plt.subplot(2, 2, 1, projection='polar')
    
    for i, company in enumerate(self.companies):
        # 计算各维度得分（0-100分）
        profitability_score = min(latest_data.loc[company, 'ROE'] * 100 / 15 * 100, 100)
        solvency_score = min((2 - latest_data.loc[company, '资产负债率']) * 50, 100)
        operational_score = 85  # 营运能力相同
        growth_score = 75 if latest_data.loc[company].get('资产增长率', 0) > 0 else 60
        
        values = [profitability_score, solvency_score, operational_score, growth_score]
        values += values[:1]  # 闭合图形
        
        ax1.plot(angles, values, 'o-', linewidth=2, label=company, color=colors[i])
        ax1.fill(angles, values, alpha=0.25, color=colors[i])
    
    ax1.set_xticks(angles[:-1])
    ax1.set_xticklabels(categories)
    ax1.set_ylim(0, 100)
    ax1.set_title('四维能力雷达图', fontsize=14, pad=20)
    ax1.legend(loc='upper right', bbox_to_anchor=(1.2, 1.0))
    
    # 2. 营业收入规模对比
    ax2 = axes[0, 1]
    revenue_data = latest_data['营业收入']
    bars2 = ax2.bar(self.companies, revenue_data, color=colors)
    ax2.set_title('营业收入规模对比(万元)', fontsize=14)
    ax2.set_ylabel('营业收入(万元)', fontsize=12)
    
    for bar, value in zip(bars2, revenue_data):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(revenue_data)*0.01,
                f'{value:.0f}', ha='center', va='bottom', fontsize=10)
    
    plt.setp(ax2.get_xticklabels(), rotation=45)
    
    # 3. 净利润规模对比
    ax3 = axes[1, 0]
    profit_data = latest_data['净利润']
    bars3 = ax3.bar(self.companies, profit_data, color=colors)
    ax3.set_title('净利润规模对比(万元)', fontsize=14)
    ax3.set_ylabel('净利润(万元)', fontsize=12)
    
    for bar, value in zip(bars3, profit_data):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2, 
                height + (max(profit_data)*0.01 if height > 0 else min(profit_data)*0.01),
                f'{value:.0f}', ha='center', 
                va='bottom' if height > 0 else 'top', fontsize=10)
    
    plt.setp(ax3.get_xticklabels(), rotation=45)
    
    # 4. 综合评分排名
    ax4 = axes[1, 1]
    
    # 计算综合得分
    scores = {}
    for company in self.companies:
        roe_score = min(latest_data.loc[company, 'ROE'] * 100 / 15 * 25, 25)
        solvency_score = min((2 - latest_data.loc[company, '资产负债率']) * 25, 25)
        operational_score = 25
        growth_score = 25 if latest_data.loc[company].get('资产增长率', 0) > 0 else 15
        total_score = roe_score + solvency_score + operational_score + growth_score
        scores[company] = total_score
    
    sorted_companies = sorted(scores.items(), key=lambda x: x[1], reverse=True)
    companies_sorted = [item[0] for item in sorted_companies]
    scores_sorted = [item[1] for item in sorted_companies]
    
    bars4 = ax4.barh(companies_sorted, scores_sorted, color=colors)
    ax4.set_title('综合评分排名', fontsize=14)
    ax4.set_xlabel('综合得分', fontsize=12)
    
    for bar, value in zip(bars4, scores_sorted):
        ax4.text(bar.get_width() + 1, bar.get_y() + bar.get_height()/2,
                f'{value:.1f}分', ha='left', va='center', fontsize=10)
    
    plt.tight_layout()
    plt.savefig('综合对比分析.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("综合对比分析图表已保存")

def create_visualizations(self):
    """创建所有可视化图表"""
    print("=== 开始创建可视化图表 ===")
    
    self.plot_profitability_analysis()
    self.plot_solvency_analysis()
    self.plot_operational_analysis()
    self.plot_growth_analysis()
    self.plot_comprehensive_comparison()
    
    print("所有可视化图表创建完成！")

# 将方法添加到类中
FinancialAnalyzer.plot_comprehensive_comparison = plot_comprehensive_comparison
FinancialAnalyzer.create_visualizations = create_visualizations

print("综合对比分析可视化方法添加完成")

# 创建财务分析器实例
analyzer = FinancialAnalyzer('4390595892600087646')
print("财务分析器实例创建完成")

# 1. 加载数据
print("=== 第1步：加载财务数据 ===")
analyzer.load_data()

# 2. 探索数据结构
print("=== 第2步：探索数据结构 ===")
analyzer.explore_data_structure()

# 3. 数据清洗和准备
print("=== 第3步：数据清洗和准备 ===")
analyzer.clean_and_prepare_data()

# 4. 查看样本数据
print("=== 第4步：查看样本数据 ===")
analyzer.examine_sample_data()

# 5. 提取财务指标
print("=== 第5步：提取财务指标 ===")
analyzer.extract_financial_indicators()

# 6. 计算增长率
print("=== 第6步：计算增长率 ===")
analyzer.calculate_growth_rates()

# 7. 创建汇总数据框
print("=== 第7步：创建汇总数据框 ===")
summary_df = analyzer.create_summary_dataframe()
print("汇总数据框形状:", summary_df.shape)
print("\n汇总数据框前几行:")
display(summary_df.head())

# 8. 创建数据透视表
print("=== 第8步：创建数据透视表 ===")
analyzer.create_pivot_tables()

# 9. 创建四个能力专门透视表
print("=== 第9步：创建四个能力专门透视表 ===")
analyzer.create_four_abilities_pivot_tables()

# 10. 创建可视化图表
print("=== 第10步：创建可视化图表 ===")
analyzer.create_visualizations()

print("\n" + "="*50)
print("🎉 财务分析完成！")
print("="*50)
print("\n📊 生成的文件：")
print("1. 财务指标汇总表.xlsx - 详细财务数据")
print("2. 财务分析数据透视表.xlsx - 数据透视表分析")
print("3. 四个能力数据透视表.xlsx - 四个能力专门透视表")
print("4. 盈利能力分析.png - 盈利能力图表")
print("5. 偿债能力分析.png - 偿债能力图表")
print("6. 营运能力分析.png - 营运能力图表")
print("7. 发展能力分析.png - 发展能力图表")
print("8. 综合对比分析.png - 综合对比图表")
print("\n✅ 所有分析任务已完成！")
print("📈 请查看生成的Excel文件和图表进行详细分析")